@extends('layouts.contentNavbarLayout')
@section('title','Berita Acara')

@section('content')
<div class="row">
    <div class="col-sm-12">
        <div class="card mb-3">
            <div class="card-header">
                <h4 class="card-title fw-bold">Berita Acara</h4>
                <small class="card-subtitle">Halaman Untuk Membuat Berita Acara</small>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="col-sm-12">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr class="text-center fw-bold">
                                    <th>No</th>
                                    <th>Nama <PERSON>ela<PERSON>gan</th>
                                    <th><PERSON><PERSON></th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $no = 1;
                                @endphp
                                @forelse ($data as $item)
                                    <tr class="text-center">
                                        <td>{{$no++}}</td>
                                        <td>
                                            <div class="row">
                                                <div class="fw-bold">{{ $item->nama_customer }}</div>
                                                <small class="text-muted">{{ $item->alamat }}</small>
                                            </div>                                                                                        
                                        </td>
                                        <td>
                                            <span class="badge bg-label-info fw-bold">
                                                {{ $item->paket->nama_paket }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($item->status_id == 3)
                                            <span class="badge bg-label-success fw-bold">
                                                AKTIF
                                            </span>
                                            @elseif($item->status_id == 9)
                                            <span class="badge bg-label-danger fw-bold">
                                                ISOLIREBILLING
                                            </span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="" class="btn btn-outline-danger btn-sm {{ $item->status_id != 9 ? 'disabled' : '' }}" data-bs-toggle="tooltip" data-bs-placement="top" title="Buat BA">
                                                <i class="bx bx-printer me-2"></i>
                                                Buat BA
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection