<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('perangkat', function (Blueprint $table) {
            $table->renameColumn('modem_id','kategori_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('perangkat', function (Blueprint $table) {
            $table->renameColumn('kategori_id', 'modem_id');
        });
    }
};
